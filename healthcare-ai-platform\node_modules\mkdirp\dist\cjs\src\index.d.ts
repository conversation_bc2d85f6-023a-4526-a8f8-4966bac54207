import { MkdirpOptions } from './opts-arg.js';
export { mkdirpManual, mkdirpManualSync } from './mkdirp-manual.js';
export { mkdirpNative, mkdirpNativeSync } from './mkdirp-native.js';
export { useNative, useNativeSync } from './use-native.js';
export declare const mkdirpSync: (path: string, opts?: MkdirpOptions) => string | void;
export declare const sync: (path: string, opts?: MkdirpOptions) => string | void;
export declare const manual: ((path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
    sync: (path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
};
export declare const manualSync: (path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
export declare const native: ((path: string, options?: MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
    sync: (path: string, options?: MkdirpOptions | undefined) => string | void | undefined;
};
export declare const nativeSync: (path: string, options?: MkdirpOptions | undefined) => string | void | undefined;
export declare const mkdirp: ((path: string, opts?: MkdirpOptions) => Promise<string | void | undefined>) & {
    mkdirpSync: (path: string, opts?: MkdirpOptions) => string | void;
    mkdirpNative: ((path: string, options?: MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: MkdirpOptions | undefined) => string | void | undefined;
    };
    mkdirpNativeSync: (path: string, options?: MkdirpOptions | undefined) => string | void | undefined;
    mkdirpManual: ((path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    };
    mkdirpManualSync: (path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    sync: (path: string, opts?: MkdirpOptions) => string | void;
    native: ((path: string, options?: MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: MkdirpOptions | undefined) => string | void | undefined;
    };
    nativeSync: (path: string, options?: MkdirpOptions | undefined) => string | void | undefined;
    manual: ((path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    };
    manualSync: (path: string, options?: MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    useNative: ((opts?: MkdirpOptions | undefined) => boolean) & {
        sync: (opts?: MkdirpOptions | undefined) => boolean;
    };
    useNativeSync: (opts?: MkdirpOptions | undefined) => boolean;
};
//# sourceMappingURL=index.d.ts.map