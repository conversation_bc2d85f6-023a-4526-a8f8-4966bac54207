import { 
  UserProfile, 
  HealthcareService, 
  ServiceRecommendation, 
  MatchFactor,
  ServiceType 
} from '../types';

// Mock healthcare services data
const mockServices: HealthcareService[] = [
  {
    id: '1',
    name: 'City General Hospital Emergency',
    type: 'emergency',
    description: '24/7 emergency care with full trauma center',
    provider: {
      id: 'p1',
      name: 'Dr. <PERSON>',
      credentials: ['MD', 'Emergency Medicine'],
      experience: 15,
      languages: ['English', 'Spanish']
    },
    location: {
      address: '123 Main St',
      city: 'Downtown',
      state: 'CA',
      zipCode: '90210',
      coordinates: { lat: 34.0522, lng: -118.2437 }
    },
    availability: {
      schedule: [
        { dayOfWeek: 0, startTime: '00:00', endTime: '23:59' },
        { dayOfWeek: 1, startTime: '00:00', endTime: '23:59' },
        { dayOfWeek: 2, startTime: '00:00', endTime: '23:59' },
        { dayOfWeek: 3, startTime: '00:00', endTime: '23:59' },
        { dayOfWeek: 4, startTime: '00:00', endTime: '23:59' },
        { dayOfWeek: 5, startTime: '00:00', endTime: '23:59' },
        { dayOfWeek: 6, startTime: '00:00', endTime: '23:59' }
      ],
      emergencyHours: true
    },
    acceptedInsurance: ['Blue Cross', 'Aetna', 'Cigna', 'Medicare', 'Medicaid'],
    specialties: ['Emergency Medicine', 'Trauma', 'Critical Care'],
    rating: 4.2,
    cost: { basePrice: 500, currency: 'USD' }
  },
  {
    id: '2',
    name: 'QuickCare Urgent Care',
    type: 'urgent-care',
    description: 'Fast, convenient urgent care for non-emergency conditions',
    provider: {
      id: 'p2',
      name: 'Dr. Michael Chen',
      credentials: ['MD', 'Family Medicine'],
      experience: 8,
      languages: ['English', 'Mandarin']
    },
    location: {
      address: '456 Oak Ave',
      city: 'Midtown',
      state: 'CA',
      zipCode: '90211',
      coordinates: { lat: 34.0622, lng: -118.2537 }
    },
    availability: {
      schedule: [
        { dayOfWeek: 1, startTime: '08:00', endTime: '20:00' },
        { dayOfWeek: 2, startTime: '08:00', endTime: '20:00' },
        { dayOfWeek: 3, startTime: '08:00', endTime: '20:00' },
        { dayOfWeek: 4, startTime: '08:00', endTime: '20:00' },
        { dayOfWeek: 5, startTime: '08:00', endTime: '20:00' },
        { dayOfWeek: 6, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 0, startTime: '10:00', endTime: '16:00' }
      ],
      nextAvailable: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes from now
    },
    acceptedInsurance: ['Blue Cross', 'Aetna', 'Humana'],
    specialties: ['Family Medicine', 'Minor Injuries', 'Flu Treatment'],
    rating: 4.5,
    cost: { basePrice: 150, currency: 'USD' }
  },
  {
    id: '3',
    name: 'Wellness Primary Care',
    type: 'primary-care',
    description: 'Comprehensive primary care and preventive medicine',
    provider: {
      id: 'p3',
      name: 'Dr. Emily Rodriguez',
      credentials: ['MD', 'Internal Medicine'],
      experience: 12,
      languages: ['English', 'Spanish']
    },
    location: {
      address: '789 Pine St',
      city: 'Uptown',
      state: 'CA',
      zipCode: '90212',
      coordinates: { lat: 34.0722, lng: -118.2637 }
    },
    availability: {
      schedule: [
        { dayOfWeek: 1, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 2, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 3, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 4, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 5, startTime: '09:00', endTime: '15:00' }
      ],
      nextAvailable: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // 2 days from now
    },
    acceptedInsurance: ['Blue Cross', 'Cigna', 'Kaiser', 'Medicare'],
    specialties: ['Internal Medicine', 'Preventive Care', 'Chronic Disease Management'],
    rating: 4.8,
    cost: { basePrice: 200, currency: 'USD' }
  }
];

class RecommendationService {
  generateRecommendations(userProfile: Partial<UserProfile>): ServiceRecommendation[] {
    const recommendations: ServiceRecommendation[] = [];

    for (const service of mockServices) {
      const score = this.calculateMatchScore(userProfile, service);
      const matchFactors = this.getMatchFactors(userProfile, service);
      const reasons = this.generateReasons(userProfile, service, matchFactors);

      recommendations.push({
        service,
        score,
        reasons,
        matchFactors,
        estimatedWaitTime: this.estimateWaitTime(service),
        distance: this.calculateDistance(userProfile.location, service.location)
      });
    }

    // Sort by score (highest first)
    return recommendations.sort((a, b) => b.score - a.score);
  }

  private calculateMatchScore(userProfile: Partial<UserProfile>, service: HealthcareService): number {
    let totalScore = 0;
    let totalWeight = 0;

    const factors = this.getMatchFactors(userProfile, service);
    
    factors.forEach(factor => {
      totalScore += factor.score * factor.weight;
      totalWeight += factor.weight;
    });

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  private getMatchFactors(userProfile: Partial<UserProfile>, service: HealthcareService): MatchFactor[] {
    const factors: MatchFactor[] = [];

    // Urgency matching
    if (userProfile.medicalInfo?.urgencyLevel) {
      const urgencyScore = this.calculateUrgencyScore(userProfile.medicalInfo.urgencyLevel, service.type);
      factors.push({
        type: 'specialty',
        weight: 0.4,
        score: urgencyScore,
        description: `Service type matches urgency level (${userProfile.medicalInfo.urgencyLevel})`
      });
    }

    // Insurance matching
    if (userProfile.insurance?.provider) {
      const insuranceScore = service.acceptedInsurance.some(ins => 
        ins.toLowerCase().includes(userProfile.insurance!.provider!.toLowerCase())
      ) ? 1.0 : 0.3;
      
      factors.push({
        type: 'insurance',
        weight: 0.25,
        score: insuranceScore,
        description: insuranceScore > 0.5 ? 'Insurance accepted' : 'Insurance may not be accepted'
      });
    }

    // Location proximity (simplified)
    if (userProfile.location?.city && service.location.city) {
      const locationScore = userProfile.location.city.toLowerCase() === service.location.city.toLowerCase() ? 1.0 : 0.6;
      factors.push({
        type: 'location',
        weight: 0.2,
        score: locationScore,
        description: locationScore > 0.8 ? 'In your area' : 'Nearby location'
      });
    }

    // Rating
    if (service.rating) {
      const ratingScore = service.rating / 5.0;
      factors.push({
        type: 'rating',
        weight: 0.1,
        score: ratingScore,
        description: `${service.rating}/5 star rating`
      });
    }

    // Availability
    const availabilityScore = service.availability.emergencyHours ? 1.0 : 0.7;
    factors.push({
      type: 'availability',
      weight: 0.05,
      score: availabilityScore,
      description: service.availability.emergencyHours ? '24/7 available' : 'Limited hours'
    });

    return factors;
  }

  private calculateUrgencyScore(urgencyLevel: string, serviceType: ServiceType): number {
    const urgencyServiceMap: Record<string, Record<ServiceType, number>> = {
      'emergency': {
        'emergency': 1.0,
        'urgent-care': 0.7,
        'primary-care': 0.2,
        'specialist': 0.1,
        'mental-health': 0.3,
        'dental': 0.2,
        'vision': 0.1,
        'pharmacy': 0.4,
        'lab': 0.3,
        'imaging': 0.3,
        'physical-therapy': 0.1,
        'home-health': 0.2
      },
      'high': {
        'emergency': 0.8,
        'urgent-care': 1.0,
        'primary-care': 0.6,
        'specialist': 0.7,
        'mental-health': 0.8,
        'dental': 0.7,
        'vision': 0.5,
        'pharmacy': 0.6,
        'lab': 0.7,
        'imaging': 0.7,
        'physical-therapy': 0.4,
        'home-health': 0.5
      },
      'medium': {
        'emergency': 0.3,
        'urgent-care': 0.8,
        'primary-care': 1.0,
        'specialist': 0.9,
        'mental-health': 0.9,
        'dental': 0.8,
        'vision': 0.8,
        'pharmacy': 0.7,
        'lab': 0.8,
        'imaging': 0.8,
        'physical-therapy': 0.8,
        'home-health': 0.7
      },
      'low': {
        'emergency': 0.1,
        'urgent-care': 0.4,
        'primary-care': 1.0,
        'specialist': 1.0,
        'mental-health': 1.0,
        'dental': 1.0,
        'vision': 1.0,
        'pharmacy': 0.9,
        'lab': 0.9,
        'imaging': 0.9,
        'physical-therapy': 1.0,
        'home-health': 0.9
      }
    };

    return urgencyServiceMap[urgencyLevel]?.[serviceType] || 0.5;
  }

  private generateReasons(userProfile: Partial<UserProfile>, service: HealthcareService, factors: MatchFactor[]): string[] {
    const reasons: string[] = [];

    // Add top reasons based on match factors
    const topFactors = factors
      .filter(f => f.score > 0.7)
      .sort((a, b) => (b.score * b.weight) - (a.score * a.weight))
      .slice(0, 3);

    topFactors.forEach(factor => {
      reasons.push(factor.description);
    });

    // Add specific reasons based on service type and user needs
    if (userProfile.medicalInfo?.urgencyLevel === 'high' && service.type === 'emergency') {
      reasons.push('Immediate care available for urgent conditions');
    }

    if (service.rating && service.rating > 4.5) {
      reasons.push('Highly rated by patients');
    }

    return reasons;
  }

  private estimateWaitTime(service: HealthcareService): string {
    switch (service.type) {
      case 'emergency':
        return 'Immediate';
      case 'urgent-care':
        return '15-45 minutes';
      case 'primary-care':
        return '1-3 days';
      case 'specialist':
        return '1-2 weeks';
      default:
        return 'Contact for availability';
    }
  }

  private calculateDistance(userLocation?: Partial<{ coordinates: { lat: number; lng: number } }>, serviceLocation?: Partial<{ coordinates: { lat: number; lng: number } }>): number | undefined {
    if (!userLocation?.coordinates || !serviceLocation?.coordinates) {
      return undefined;
    }

    // Simplified distance calculation (in miles)
    const lat1 = userLocation.coordinates.lat;
    const lon1 = userLocation.coordinates.lng;
    const lat2 = serviceLocation.coordinates.lat;
    const lon2 = serviceLocation.coordinates.lng;

    const R = 3959; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    return Math.round(distance * 10) / 10; // Round to 1 decimal place
  }
}

export const recommendationService = new RecommendationService();
