import React, { useRef, useEffect } from 'react';
import { useChat } from '../../hooks/useChat';
import { UserProfile } from '../../types';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { TypingIndicator } from './TypingIndicator';

interface ChatProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
  isMinimized: boolean;
  userProfile: Partial<UserProfile>;
  onDataExtracted: (data: Partial<UserProfile>) => void;
}

export function Chat({ 
  isOpen, 
  onClose, 
  onMinimize, 
  isMinimized, 
  userProfile, 
  onDataExtracted 
}: ChatProps) {
  const { chatState, optimisticMessages, actions, isPending } = useChat(userProfile);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && !isMinimized) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [optimisticMessages, isMinimized]);

  // Handle form submission with extracted data
  const handleMessageSent = async (formData: FormData) => {
    const result = await actions.sendMessage(formData);
    
    // If data was extracted, notify parent component
    if (result && result.extractedData) {
      onDataExtracted(result.extractedData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div 
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 transition-all duration-300 ${
          isMinimized 
            ? 'w-80 h-16' 
            : 'w-96 h-[600px] max-h-[80vh]'
        }`}
      >
        {/* Chat Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-healthcare-600 text-white rounded-t-2xl">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
            </div>
            <div>
              <h3 className="font-semibold text-sm">HealthAI Assistant</h3>
              <p className="text-xs text-healthcare-100">
                {chatState.isTyping ? 'Typing...' : 'Online'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={onMinimize}
              className="p-1 hover:bg-white/20 rounded transition-colors duration-200"
              aria-label="Minimize chat"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 12H4"
                />
              </svg>
            </button>
            <button
              onClick={onClose}
              className="p-1 hover:bg-white/20 rounded transition-colors duration-200"
              aria-label="Close chat"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Chat Content - Hidden when minimized */}
        {!isMinimized && (
          <>
            {/* Messages Container */}
            <div 
              ref={chatContainerRef}
              className="flex-1 overflow-y-auto p-4 space-y-4 h-[400px]"
            >
              {optimisticMessages.length === 0 ? (
                <div className="text-center text-gray-500 mt-8">
                  <div className="w-16 h-16 bg-healthcare-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-8 h-8 text-healthcare-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                  </div>
                  <p className="text-sm">
                    Hi! I'm your AI healthcare assistant. Tell me about your symptoms or what kind of care you're looking for.
                  </p>
                </div>
              ) : (
                optimisticMessages.map((message) => (
                  <ChatMessage key={message.id} message={message} />
                ))
              )}

              {/* Typing Indicator */}
              {chatState.isTyping && <TypingIndicator />}

              {/* Error Message */}
              {chatState.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-800 text-sm">{chatState.error}</p>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4">
              <ChatInput 
                onSubmit={handleMessageSent}
                disabled={isPending || chatState.isLoading}
                placeholder="Describe your symptoms or healthcare needs..."
              />
            </div>
          </>
        )}

        {/* Minimized State Content */}
        {isMinimized && (
          <div 
            className="flex items-center justify-between p-4 cursor-pointer"
            onClick={() => onMinimize()}
          >
            <span className="text-sm text-gray-600">
              {chatState.isTyping ? 'AI is typing...' : 'Click to expand chat'}
            </span>
            {optimisticMessages.length > 0 && (
              <span className="bg-healthcare-600 text-white text-xs px-2 py-1 rounded-full">
                {optimisticMessages.length}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
