// User and demographic types
export interface UserProfile {
  id?: string;
  demographics: Demographics;
  preferences: UserPreferences;
  medicalInfo: MedicalInfo;
  location?: Location;
  insurance?: InsuranceInfo;
}

export interface Demographics {
  age?: number;
  gender?: 'male' | 'female' | 'other' | 'prefer-not-to-say';
  occupation?: string;
  emergencyContact?: string;
}

export interface UserPreferences {
  preferredLanguage?: string;
  communicationMethod?: 'phone' | 'email' | 'text' | 'app';
  appointmentTimes?: string[];
  specialRequirements?: string[];
}

export interface MedicalInfo {
  symptoms?: string[];
  conditions?: string[];
  medications?: string[];
  allergies?: string[];
  urgencyLevel?: 'low' | 'medium' | 'high' | 'emergency';
}

export interface Location {
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface InsuranceInfo {
  provider?: string;
  planType?: string;
  memberId?: string;
  groupNumber?: string;
}

// Chat and AI types
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'structured-data' | 'recommendation';
  metadata?: Record<string, any>;
}

export interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  isTyping: boolean;
  error?: string;
}

export interface AIResponse {
  message: string;
  extractedData?: Partial<UserProfile>;
  recommendations?: ServiceRecommendation[];
  confidence?: number;
}

// Healthcare service types
export interface HealthcareService {
  id: string;
  name: string;
  type: ServiceType;
  description: string;
  provider: HealthcareProvider;
  location: Location;
  availability: Availability;
  acceptedInsurance: string[];
  specialties: string[];
  rating?: number;
  reviews?: Review[];
  cost?: CostInfo;
}

export interface ServiceRecommendation {
  service: HealthcareService;
  score: number;
  reasons: string[];
  matchFactors: MatchFactor[];
  estimatedWaitTime?: string;
  distance?: number;
}

export interface HealthcareProvider {
  id: string;
  name: string;
  credentials: string[];
  experience: number;
  languages: string[];
  photo?: string;
}

export interface Availability {
  schedule: TimeSlot[];
  nextAvailable?: Date;
  emergencyHours?: boolean;
}

export interface TimeSlot {
  dayOfWeek: number; // 0-6, Sunday = 0
  startTime: string; // HH:MM format
  endTime: string;   // HH:MM format
}

export interface Review {
  id: string;
  rating: number;
  comment: string;
  date: Date;
  verified: boolean;
}

export interface CostInfo {
  basePrice?: number;
  insuranceCovered?: boolean;
  estimatedCopay?: number;
  currency: string;
}

export type ServiceType = 
  | 'primary-care'
  | 'urgent-care'
  | 'emergency'
  | 'specialist'
  | 'mental-health'
  | 'dental'
  | 'vision'
  | 'pharmacy'
  | 'lab'
  | 'imaging'
  | 'physical-therapy'
  | 'home-health';

export interface MatchFactor {
  type: 'location' | 'insurance' | 'specialty' | 'availability' | 'rating' | 'cost';
  weight: number;
  score: number;
  description: string;
}

// UI State types
export interface AppState {
  user: UserProfile | null;
  chat: ChatState;
  recommendations: ServiceRecommendation[];
  isOnboarding: boolean;
  isChatOpen: boolean;
  isLoading: boolean;
  error?: string;
}

// API types
export interface APIError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Form types for React 19 hooks
export interface ChatFormData {
  message: string;
}

export interface OnboardingFormData {
  demographics: Partial<Demographics>;
  preferences: Partial<UserPreferences>;
  medicalInfo: Partial<MedicalInfo>;
  location: Partial<Location>;
  insurance: Partial<InsuranceInfo>;
}
