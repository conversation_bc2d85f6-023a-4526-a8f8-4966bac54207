import { useState, useCallback, useMemo } from 'react';
import { UserProfile, ServiceRecommendation } from '../types';
import { recommendationService } from '../services/recommendationService';

interface UseUserProfileReturn {
  userProfile: Partial<UserProfile>;
  recommendations: ServiceRecommendation[];
  isNewUser: boolean;
  updateProfile: (updates: Partial<UserProfile>) => void;
  mergeExtractedData: (extractedData: Partial<UserProfile>) => void;
  generateRecommendations: () => void;
  clearProfile: () => void;
}

export function useUserProfile(): UseUserProfileReturn {
  const [userProfile, setUserProfile] = useState<Partial<UserProfile>>({
    demographics: {},
    preferences: {},
    medicalInfo: {},
    location: {},
    insurance: {}
  });

  const [recommendations, setRecommendations] = useState<ServiceRecommendation[]>([]);

  // Determine if user is new based on available data
  const isNewUser = useMemo(() => {
    const hasBasicInfo = userProfile.demographics?.age || 
                        userProfile.demographics?.gender ||
                        userProfile.location?.city ||
                        userProfile.medicalInfo?.symptoms?.length;
    return !hasBasicInfo;
  }, [userProfile]);

  const updateProfile = useCallback((updates: Partial<UserProfile>) => {
    setUserProfile(prev => ({
      demographics: { ...prev.demographics, ...updates.demographics },
      preferences: { ...prev.preferences, ...updates.preferences },
      medicalInfo: { ...prev.medicalInfo, ...updates.medicalInfo },
      location: { ...prev.location, ...updates.location },
      insurance: { ...prev.insurance, ...updates.insurance },
      ...updates
    }));
  }, []);

  const mergeExtractedData = useCallback((extractedData: Partial<UserProfile>) => {
    setUserProfile(prev => {
      const merged: Partial<UserProfile> = { ...prev };

      // Merge demographics
      if (extractedData.demographics) {
        merged.demographics = { ...prev.demographics, ...extractedData.demographics };
      }

      // Merge medical info - be smart about symptoms
      if (extractedData.medicalInfo) {
        merged.medicalInfo = { ...prev.medicalInfo };
        
        // Merge symptoms without duplicates
        if (extractedData.medicalInfo.symptoms) {
          const existingSymptoms = prev.medicalInfo?.symptoms || [];
          const newSymptoms = extractedData.medicalInfo.symptoms.filter(
            symptom => !existingSymptoms.includes(symptom)
          );
          merged.medicalInfo.symptoms = [...existingSymptoms, ...newSymptoms];
        }

        // Update other medical info
        Object.keys(extractedData.medicalInfo).forEach(key => {
          if (key !== 'symptoms' && extractedData.medicalInfo![key as keyof typeof extractedData.medicalInfo]) {
            (merged.medicalInfo as any)[key] = extractedData.medicalInfo![key as keyof typeof extractedData.medicalInfo];
          }
        });
      }

      // Merge location
      if (extractedData.location) {
        merged.location = { ...prev.location, ...extractedData.location };
      }

      // Merge insurance
      if (extractedData.insurance) {
        merged.insurance = { ...prev.insurance, ...extractedData.insurance };
      }

      // Merge preferences
      if (extractedData.preferences) {
        merged.preferences = { ...prev.preferences, ...extractedData.preferences };
      }

      return merged;
    });
  }, []);

  const generateRecommendations = useCallback(() => {
    try {
      const newRecommendations = recommendationService.generateRecommendations(userProfile);
      setRecommendations(newRecommendations);
    } catch (error) {
      console.error('Error generating recommendations:', error);
      setRecommendations([]);
    }
  }, [userProfile]);

  const clearProfile = useCallback(() => {
    setUserProfile({
      demographics: {},
      preferences: {},
      medicalInfo: {},
      location: {},
      insurance: {}
    });
    setRecommendations([]);
  }, []);

  return {
    userProfile,
    recommendations,
    isNewUser,
    updateProfile,
    mergeExtractedData,
    generateRecommendations,
    clearProfile
  };
}

// Hook for managing app-wide state
export function useAppState() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [isOnboarding, setIsOnboarding] = useState(true);

  const setLoadingState = useCallback((loading: boolean) => {
    setIsLoading(loading);
    if (loading) {
      setError(undefined);
    }
  }, []);

  const setErrorState = useCallback((errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
  }, []);

  const clearError = useCallback(() => {
    setError(undefined);
  }, []);

  const completeOnboarding = useCallback(() => {
    setIsOnboarding(false);
  }, []);

  const resetOnboarding = useCallback(() => {
    setIsOnboarding(true);
  }, []);

  return {
    isLoading,
    error,
    isOnboarding,
    setLoadingState,
    setErrorState,
    clearError,
    completeOnboarding,
    resetOnboarding
  };
}
