import React, { useMemo } from 'react';
import { ServiceRecommendation } from '../../types';
import { RecommendationCard } from './RecommendationCard';

interface RecommendationsProps {
  recommendations: ServiceRecommendation[];
  isLoading?: boolean;
  onServiceSelect?: (recommendation: ServiceRecommendation) => void;
}

export function Recommendations({ 
  recommendations, 
  isLoading = false, 
  onServiceSelect 
}: RecommendationsProps) {
  
  const sortedRecommendations = useMemo(() => {
    return [...recommendations].sort((a, b) => b.score - a.score);
  }, [recommendations]);

  const getRecommendationTier = (score: number) => {
    if (score >= 0.8) return 'excellent';
    if (score >= 0.6) return 'good';
    if (score >= 0.4) return 'fair';
    return 'poor';
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <div className="inline-flex items-center space-x-2 text-healthcare-600">
            <svg
              className="w-6 h-6 animate-spin"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            <span className="text-lg font-medium">Finding the best healthcare options for you...</span>
          </div>
        </div>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center bg-gray-50 rounded-2xl p-8">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No recommendations yet
          </h3>
          <p className="text-gray-600 mb-4">
            Start a conversation with our AI assistant to get personalized healthcare recommendations.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Your Healthcare Recommendations
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Based on your needs, here are the best healthcare options we found for you, 
          ranked by relevance and quality.
        </p>
      </div>

      {/* Recommendations Grid */}
      <div className="space-y-6">
        {sortedRecommendations.map((recommendation, index) => {
          const tier = getRecommendationTier(recommendation.score);
          
          return (
            <div key={recommendation.service.id} className="relative">
              {/* Ranking Badge */}
              <div className="absolute -left-4 top-4 z-10">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                  index === 0 ? 'bg-yellow-500' :
                  index === 1 ? 'bg-gray-400' :
                  index === 2 ? 'bg-orange-600' :
                  'bg-gray-300'
                }`}>
                  {index + 1}
                </div>
              </div>

              {/* Best Match Badge */}
              {index === 0 && (
                <div className="absolute -top-2 left-8 z-10">
                  <span className="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                    Best Match
                  </span>
                </div>
              )}

              <RecommendationCard
                recommendation={recommendation}
                tier={tier}
                onSelect={onServiceSelect}
                className={index === 0 ? 'ring-2 ring-healthcare-200' : ''}
              />
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      <div className="mt-12 bg-healthcare-50 rounded-2xl p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div>
            <div className="text-2xl font-bold text-healthcare-600">
              {recommendations.length}
            </div>
            <div className="text-sm text-gray-600">Options Found</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-healthcare-600">
              {recommendations.filter(r => r.score >= 0.8).length}
            </div>
            <div className="text-sm text-gray-600">Excellent Matches</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-healthcare-600">
              {recommendations.filter(r => r.estimatedWaitTime === 'Immediate' || r.estimatedWaitTime?.includes('minutes')).length}
            </div>
            <div className="text-sm text-gray-600">Available Today</div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-8 text-center">
        <p className="text-gray-600 mb-4">
          Need help choosing? Our AI assistant can provide more personalized guidance.
        </p>
        <button
          onClick={() => {/* This would open chat */}}
          className="btn-primary"
        >
          Ask AI for Help
        </button>
      </div>
    </div>
  );
}
