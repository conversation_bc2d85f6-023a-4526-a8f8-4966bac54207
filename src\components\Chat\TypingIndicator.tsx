import React from 'react';

export function TypingIndicator() {
  return (
    <div className="flex justify-start animate-slide-up">
      <div className="bg-gray-100 text-gray-800 max-w-xs lg:max-w-md px-4 py-3 rounded-2xl">
        <div className="flex items-center space-x-1">
          <span className="text-sm text-gray-600">AI is typing</span>
          <div className="loading-dots">
            <div className="animate-pulse" style={{ animationDelay: '0ms' }}></div>
            <div className="animate-pulse" style={{ animationDelay: '150ms' }}></div>
            <div className="animate-pulse" style={{ animationDelay: '300ms' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
}
