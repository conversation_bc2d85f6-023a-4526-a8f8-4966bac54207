import React from 'react';
import { ServiceRecommendation } from '../../types';

interface RecommendationCardProps {
  recommendation: ServiceRecommendation;
  tier: 'excellent' | 'good' | 'fair' | 'poor';
  onSelect?: (recommendation: ServiceRecommendation) => void;
  className?: string;
}

export function RecommendationCard({ 
  recommendation, 
  tier, 
  onSelect, 
  className = '' 
}: RecommendationCardProps) {
  const { service, score, reasons, matchFactors, estimatedWaitTime, distance } = recommendation;

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'excellent': return 'text-green-600 bg-green-50 border-green-200';
      case 'good': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'fair': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'emergency':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'urgent-care':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'primary-care':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
    }
  };

  const formatServiceType = (type: string) => {
    return type.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className={`bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className="flex items-center space-x-2 text-healthcare-600">
                {getServiceTypeIcon(service.type)}
                <span className="text-sm font-medium text-gray-600">
                  {formatServiceType(service.type)}
                </span>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getTierColor(tier)}`}>
                {Math.round(score * 100)}% Match
              </div>
            </div>
            
            <h3 className="text-xl font-bold text-gray-900 mb-1">
              {service.name}
            </h3>
            
            <p className="text-gray-600 text-sm mb-3">
              {service.description}
            </p>

            {/* Provider Info */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>{service.provider.name}</span>
              </div>
              
              {service.rating && (
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                  <span>{service.rating}/5</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Key Information Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {estimatedWaitTime}
            </div>
            <div className="text-xs text-gray-500">Wait Time</div>
          </div>
          
          {distance && (
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {distance} mi
              </div>
              <div className="text-xs text-gray-500">Distance</div>
            </div>
          )}
          
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {service.location.city}
            </div>
            <div className="text-xs text-gray-500">Location</div>
          </div>
          
          {service.cost && (
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                ${service.cost.basePrice}
              </div>
              <div className="text-xs text-gray-500">Est. Cost</div>
            </div>
          )}
        </div>

        {/* Reasons */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Why this is a good match:</h4>
          <ul className="space-y-1">
            {reasons.slice(0, 3).map((reason, index) => (
              <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                <svg className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>{reason}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Specialties */}
        {service.specialties.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-900 mb-2">Specialties:</h4>
            <div className="flex flex-wrap gap-2">
              {service.specialties.slice(0, 3).map((specialty, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 bg-healthcare-50 text-healthcare-700 text-xs rounded-full"
                >
                  {specialty}
                </span>
              ))}
              {service.specialties.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  +{service.specialties.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={() => onSelect?.(recommendation)}
            className="flex-1 btn-primary text-sm py-2"
          >
            Select Provider
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
            View Details
          </button>
        </div>
      </div>
    </div>
  );
}
