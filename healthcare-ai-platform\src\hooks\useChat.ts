import { useState, useCallback, useOptimistic, useActionState } from 'react';
import { ChatMessage, ChatState, UserProfile, AIResponse } from '../types';
import { aiService } from '../services/aiService';

interface ChatFormData {
  message: string;
}

interface ChatActions {
  sendMessage: (formData: FormData) => Promise<void>;
  clearChat: () => void;
  setTyping: (isTyping: boolean) => void;
}

interface UseChatReturn {
  chatState: ChatState;
  optimisticMessages: ChatMessage[];
  actions: ChatActions;
  isPending: boolean;
}

export function useChat(userProfile?: Partial<UserProfile>): UseChatReturn {
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    isTyping: false,
    error: undefined
  });

  // Use React 19's useOptimistic for immediate UI updates
  const [optimisticMessages, addOptimisticMessage] = useOptimistic(
    chatState.messages,
    (state: ChatMessage[], newMessage: ChatMessage) => [...state, newMessage]
  );

  // Use React 19's useActionState for form handling
  const [isPending, formAction] = useActionState(
    async (prevState: any, formData: FormData) => {
      const message = formData.get('message') as string;
      if (!message.trim()) return prevState;

      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        content: message.trim(),
        sender: 'user',
        timestamp: new Date(),
        type: 'text'
      };

      // Optimistically add user message
      addOptimisticMessage(userMessage);

      try {
        // Set typing indicator
        setChatState(prev => ({ ...prev, isTyping: true, error: undefined }));

        // Send message to AI service
        const aiResponse: AIResponse = await aiService.sendMessage(message, userProfile);

        // Create AI response message
        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          content: aiResponse.message,
          sender: 'ai',
          timestamp: new Date(),
          type: 'text',
          metadata: {
            extractedData: aiResponse.extractedData,
            confidence: aiResponse.confidence
          }
        };

        // Update chat state with both messages
        setChatState(prev => ({
          ...prev,
          messages: [...prev.messages, userMessage, aiMessage],
          isTyping: false,
          isLoading: false
        }));

        // Return extracted data for parent component to handle
        return {
          success: true,
          extractedData: aiResponse.extractedData,
          recommendations: aiResponse.recommendations
        };

      } catch (error) {
        console.error('Chat error:', error);
        
        // Add error message
        const errorMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          content: "I'm sorry, I'm having trouble connecting right now. Please try again.",
          sender: 'ai',
          timestamp: new Date(),
          type: 'text'
        };

        setChatState(prev => ({
          ...prev,
          messages: [...prev.messages, userMessage, errorMessage],
          isTyping: false,
          isLoading: false,
          error: 'Failed to send message'
        }));

        return { success: false, error: 'Failed to send message' };
      }
    },
    { success: false }
  );

  const clearChat = useCallback(() => {
    setChatState({
      messages: [],
      isLoading: false,
      isTyping: false,
      error: undefined
    });
  }, []);

  const setTyping = useCallback((isTyping: boolean) => {
    setChatState(prev => ({ ...prev, isTyping }));
  }, []);

  const sendMessage = useCallback(async (formData: FormData) => {
    await formAction(formData);
  }, [formAction]);

  return {
    chatState,
    optimisticMessages,
    actions: {
      sendMessage,
      clearChat,
      setTyping
    },
    isPending
  };
}

// Hook for managing chat UI state
export function useChatUI() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const toggleChat = useCallback(() => {
    setIsOpen(prev => !prev);
    if (isMinimized) {
      setIsMinimized(false);
    }
  }, [isMinimized]);

  const minimizeChat = useCallback(() => {
    setIsMinimized(true);
  }, []);

  const maximizeChat = useCallback(() => {
    setIsMinimized(false);
  }, []);

  const closeChat = useCallback(() => {
    setIsOpen(false);
    setIsMinimized(false);
  }, []);

  return {
    isOpen,
    isMinimized,
    toggleChat,
    minimizeChat,
    maximizeChat,
    closeChat
  };
}
