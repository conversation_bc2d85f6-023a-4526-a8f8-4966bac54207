import React, { useEffect } from 'react';
import { Navigation } from './components/Navigation/Navigation';
import { Hero } from './components/Hero/Hero';
import { Chat } from './components/Chat/Chat';
import { Recommendations } from './components/Recommendations/Recommendations';
import { useUserProfile, useAppState } from './hooks/useUserProfile';
import { useChatUI } from './hooks/useChat';
import { recommendationService } from './services/recommendationService';

function App() {
  const {
    userProfile,
    recommendations,
    isNewUser,
    mergeExtractedData,
    generateRecommendations
  } = useUserProfile();

  const {
    isLoading,
    error,
    isOnboarding,
    setLoadingState,
    completeOnboarding
  } = useAppState();

  const {
    isOpen: isChatOpen,
    isMinimized: isChatMinimized,
    toggleChat,
    minimizeChat,
    maximizeChat,
    closeChat
  } = useChatUI();

  // Handle data extraction from chat
  const handleDataExtracted = (extractedData: Partial<typeof userProfile>) => {
    mergeExtractedData(extractedData);

    // Complete onboarding if we have enough data
    if (isOnboarding && (extractedData.medicalInfo?.symptoms?.length || extractedData.demographics?.age)) {
      completeOnboarding();
    }
  };

  // Generate recommendations when user profile changes
  useEffect(() => {
    if (!isNewUser && (userProfile.medicalInfo?.symptoms?.length || userProfile.demographics?.age)) {
      setLoadingState(true);

      // Simulate processing time for better UX
      setTimeout(() => {
        generateRecommendations();
        setLoadingState(false);
      }, 1500);
    }
  }, [userProfile, isNewUser, generateRecommendations, setLoadingState]);

  const handleGetStarted = () => {
    toggleChat();
  };

  const handleChatToggle = () => {
    if (isChatMinimized) {
      maximizeChat();
    } else {
      toggleChat();
    }
  };

  const handleServiceSelect = (recommendation: any) => {
    console.log('Selected service:', recommendation);
    // Here you would typically handle booking or more detailed view
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation
        onChatToggle={handleChatToggle}
        isChatOpen={isChatOpen}
        hasUnreadMessages={false}
      />

      {/* Main Content */}
      <main className="pt-16">
        {/* Hero Section */}
        <Hero
          userProfile={userProfile}
          isNewUser={isNewUser}
          onGetStarted={handleGetStarted}
          onChatOpen={toggleChat}
        />

        {/* Recommendations Section */}
        {!isNewUser && (
          <section className="py-16 bg-white">
            <Recommendations
              recommendations={recommendations}
              isLoading={isLoading}
              onServiceSelect={handleServiceSelect}
            />
          </section>
        )}

        {/* Error Display */}
        {error && (
          <div className="fixed bottom-20 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg z-40">
            <div className="flex items-start space-x-3">
              <svg className="w-5 h-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800">Something went wrong</h4>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Chat Component */}
      <Chat
        isOpen={isChatOpen}
        onClose={closeChat}
        onMinimize={minimizeChat}
        isMinimized={isChatMinimized}
        userProfile={userProfile}
        onDataExtracted={handleDataExtracted}
      />
    </div>
  );
}

export default App;
