import { AIResponse, ChatMessage, UserProfile } from '../types';

// Mock AI service - replace with actual 21st.dev API integration
class AIService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    // In a real app, these would come from environment variables
    this.apiKey = process.env.VITE_AI_API_KEY || 'demo-key';
    this.baseUrl = process.env.VITE_AI_BASE_URL || 'https://api.21st.dev';
  }

  async sendMessage(message: string, context?: Partial<UserProfile>): Promise<AIResponse> {
    try {
      // For demo purposes, we'll simulate the AI response
      // In production, replace this with actual API call to 21st.dev
      const response = await this.simulateAIResponse(message, context);
      return response;
    } catch (error) {
      console.error('AI Service Error:', error);
      throw new Error('Failed to get AI response');
    }
  }

  private async simulateAIResponse(message: string, context?: Partial<UserProfile>): Promise<AIResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const extractedData = this.extractDataFromMessage(message);
    const aiMessage = this.generateAIResponse(message, extractedData, context);

    return {
      message: aiMessage,
      extractedData,
      confidence: 0.85
    };
  }

  private extractDataFromMessage(message: string): Partial<UserProfile> {
    const extracted: Partial<UserProfile> = {
      demographics: {},
      preferences: {},
      medicalInfo: {},
      location: {},
      insurance: {}
    };

    const lowerMessage = message.toLowerCase();

    // Extract age
    const ageMatch = lowerMessage.match(/(\d+)\s*years?\s*old|age\s*(\d+)|i'm\s*(\d+)/);
    if (ageMatch) {
      extracted.demographics!.age = parseInt(ageMatch[1] || ageMatch[2] || ageMatch[3]);
    }

    // Extract gender
    if (lowerMessage.includes('male') && !lowerMessage.includes('female')) {
      extracted.demographics!.gender = 'male';
    } else if (lowerMessage.includes('female')) {
      extracted.demographics!.gender = 'female';
    }

    // Extract symptoms
    const symptoms: string[] = [];
    const symptomKeywords = [
      'headache', 'fever', 'cough', 'sore throat', 'nausea', 'dizziness',
      'chest pain', 'back pain', 'stomach pain', 'fatigue', 'shortness of breath'
    ];
    
    symptomKeywords.forEach(symptom => {
      if (lowerMessage.includes(symptom)) {
        symptoms.push(symptom);
      }
    });
    
    if (symptoms.length > 0) {
      extracted.medicalInfo!.symptoms = symptoms;
    }

    // Extract urgency
    if (lowerMessage.includes('emergency') || lowerMessage.includes('urgent') || lowerMessage.includes('severe')) {
      extracted.medicalInfo!.urgencyLevel = 'high';
    } else if (lowerMessage.includes('mild') || lowerMessage.includes('minor')) {
      extracted.medicalInfo!.urgencyLevel = 'low';
    } else {
      extracted.medicalInfo!.urgencyLevel = 'medium';
    }

    // Extract location
    const locationMatch = lowerMessage.match(/in\s+([a-zA-Z\s]+),?\s*([a-zA-Z]{2})?/);
    if (locationMatch) {
      extracted.location!.city = locationMatch[1].trim();
      if (locationMatch[2]) {
        extracted.location!.state = locationMatch[2].trim();
      }
    }

    // Extract insurance
    const insuranceProviders = ['blue cross', 'aetna', 'cigna', 'humana', 'kaiser', 'medicare', 'medicaid'];
    insuranceProviders.forEach(provider => {
      if (lowerMessage.includes(provider)) {
        extracted.insurance!.provider = provider;
      }
    });

    return extracted;
  }

  private generateAIResponse(userMessage: string, extractedData: Partial<UserProfile>, context?: Partial<UserProfile>): string {
    const responses = [
      "I understand you're looking for healthcare assistance. Let me help you find the right care.",
      "Thank you for sharing that information. I'm here to help you find appropriate healthcare services.",
      "I can help you find healthcare providers that match your needs. Let me gather a bit more information.",
      "Based on what you've told me, I can recommend some healthcare options for you."
    ];

    let response = responses[Math.floor(Math.random() * responses.length)];

    // Add specific responses based on extracted data
    if (extractedData.medicalInfo?.symptoms?.length) {
      response += ` I see you're experiencing ${extractedData.medicalInfo.symptoms.join(', ')}.`;
    }

    if (extractedData.medicalInfo?.urgencyLevel === 'high') {
      response += " Given the urgency of your situation, I'll prioritize urgent care and emergency options.";
    }

    if (extractedData.location?.city) {
      response += ` I'll look for providers in ${extractedData.location.city}.`;
    }

    response += " Is there anything else you'd like me to know about your healthcare needs?";

    return response;
  }

  // Method to be called when integrating with real 21st.dev API
  private async callRealAPI(message: string, context?: Partial<UserProfile>): Promise<AIResponse> {
    const response = await fetch(`${this.baseUrl}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        message,
        context,
        systemPrompt: `You are a healthcare AI assistant that helps users find appropriate healthcare services. 
        Extract relevant information from user messages including demographics, symptoms, location, insurance, and preferences. 
        Provide helpful, empathetic responses while gathering information needed to make healthcare recommendations.
        Always prioritize user safety and recommend emergency services when appropriate.`
      })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return await response.json();
  }
}

export const aiService = new AIService();
