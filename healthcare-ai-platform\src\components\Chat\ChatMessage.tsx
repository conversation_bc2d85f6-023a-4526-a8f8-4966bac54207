import React from 'react';
import { ChatMessage as ChatMessageType } from '../../types';

interface ChatMessageProps {
  message: ChatMessageType;
}

export function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.sender === 'user';
  
  const formatTime = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).format(timestamp);
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} animate-slide-up`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
        isUser 
          ? 'bg-healthcare-600 text-white' 
          : 'bg-gray-100 text-gray-800'
      }`}>
        {/* Message Content */}
        <p className="text-sm leading-relaxed whitespace-pre-wrap">
          {message.content}
        </p>
        
        {/* Timestamp */}
        <p className={`text-xs mt-1 ${
          isUser ? 'text-healthcare-100' : 'text-gray-500'
        }`}>
          {formatTime(message.timestamp)}
        </p>

        {/* Metadata for AI messages */}
        {!isUser && message.metadata?.extractedData && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              ✓ Information extracted
            </p>
          </div>
        )}

        {/* Confidence indicator for AI messages */}
        {!isUser && message.metadata?.confidence && (
          <div className="mt-1">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-xs text-gray-500">
                {Math.round(message.metadata.confidence * 100)}% confident
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
